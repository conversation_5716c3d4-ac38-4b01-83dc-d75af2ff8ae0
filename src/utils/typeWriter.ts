export class Typewriter {
  private queue: string[] = [];

  private consuming = false;

  private timer: ReturnType<typeof setTimeout>;

  private lastContent = ''; // 记录上次的完整内容

  private displayedContent = ''; // 当前已显示的内容

  private isFinished = false; // 标记是否已完成

  private onComplete?: () => void; // 完成回调

  constructor(
    private onConsume: (str: string) => void,
    onComplete?: () => void,
  ) {
    this.onComplete = onComplete;
  }

  // 优化的动态速度：更快的打字效果，减少卡顿感
  dynamicSpeed() {
    // 如果已标记完成，使用20ms速度来消费剩余队列
    if (this.isFinished) {
      return 20; // 完成后使用20ms速度
    }

    // 基础速度：30ms（快速但不会太快）
    const baseSpeed = 30;

    // 如果队列为空，使用基础速度
    if (this.queue.length === 0) {
      return baseSpeed;
    }

    // 如果队列积压严重，使用更快的速度
    if (this.queue.length > 10) {
      return 20; // 较快速度
    }

    return baseSpeed;
  }

  // 添加字符串到队列（处理累积文本）
  add(str: string) {
    if (!str) return;

    // 如果新内容和上次内容相同，跳过
    if (str === this.lastContent) {
      return;
    }

    // 如果新内容是上次内容的扩展，只添加增量部分
    if (str.startsWith(this.lastContent)) {
      const incrementalText = str.substring(this.lastContent.length);
      if (incrementalText) {
        this.queue.push(incrementalText);
      }
    } else if (str.startsWith(this.displayedContent)) {
      // 如果新内容包含了当前已显示的内容，只添加新增部分
      const incrementalText = str.substring(this.displayedContent.length);
      if (incrementalText) {
        // 修复：不清空队列，而是追加新内容，避免显示跳跃
        // 检查队列中是否已经包含这部分内容，避免重复添加
        const queueContent = this.queue.join('');
        if (!queueContent.includes(incrementalText)) {
          this.queue.push(incrementalText);
        }
      }
    } else if (str.length > this.displayedContent.length) {
      // 如果新内容比当前显示内容长但不包含已显示内容
      // 可能是服务器重新发送，检查是否应该从当前位置继续
      const possibleIncrement = str.substring(this.displayedContent.length);
      if (possibleIncrement) {
        // 检查队列中是否已经包含这部分内容
        const queueContent = this.queue.join('');
        if (!queueContent.includes(possibleIncrement)) {
          this.queue.push(possibleIncrement);
        }
      }
    } else {
      // 完全不同的内容，这种情况下才重新开始
      console.warn('[Typewriter] 收到完全不同的内容，重新开始显示');
      this.queue = [str];
      this.displayedContent = '';
    }

    this.lastContent = str;
  }

  // 消费
  consume() {
    if (this.queue.length > 0) {
      const incrementalText = this.queue.shift();
      if (incrementalText) {
        // 累积显示的内容
        this.displayedContent += incrementalText;
        this.onConsume(this.displayedContent);
      }
    } else if (this.isFinished && this.consuming) {
      // 队列消费完毕且已标记完成，自动结束
      this.consuming = false;
      clearTimeout(this.timer);
      if (this.onComplete) {
        this.onComplete();
      }
    }
  }

  // 持续消费
  loopConsume() {
    this.consume();
    // 根据队列中字符的数量来设置消耗每一帧的速度，用定时器消耗
    this.timer = setTimeout(() => {
      if (this.consuming) {
        this.loopConsume();
      }
    }, this.dynamicSpeed());
  }

  // 开始消费队列
  start() {
    this.displayedContent = ''; // 重置已显示内容
    this.consuming = true;
    this.loopConsume();
  }

  // 结束消费队列
  done() {
    this.consuming = false;
    clearTimeout(this.timer);

    // 修复：避免直接覆盖显示内容，保持渐进式显示
    if (this.queue.length > 0) {
      // 如果还有队列内容，快速消费完剩余队列而不是直接跳到最终内容
      while (this.queue.length > 0) {
        const incrementalText = this.queue.shift();
        if (incrementalText) {
          this.displayedContent += incrementalText;
        }
      }
      // 显示最终的累积内容
      this.onConsume(this.displayedContent);
    } else if (this.lastContent && this.lastContent !== this.displayedContent) {
      // 如果最终内容与已显示内容不同，但确保这是正确的最终状态
      this.onConsume(this.lastContent);
    }

    // 重置状态
    this.lastContent = '';
    this.displayedContent = '';
  }

  // 标记完成（不立即结束，等队列消费完毕）
  markFinished() {
    this.isFinished = true;
    // 调试信息：打印队列状态
    console.log('[Typewriter] markFinished 调用');
    console.log('[Typewriter] 队列长度:', this.queue.length);
    console.log('[Typewriter] 队列内容:', this.queue);
    console.log('[Typewriter] 当前显示内容长度:', this.displayedContent.length);
    console.log('[Typewriter] 上次内容长度:', this.lastContent.length);
    console.log('[Typewriter] 正在消费:', this.consuming);
    // 如果队列为空且正在消费，立即完成
    if (this.queue.length === 0 && this.consuming) {
      console.log('[Typewriter] 队列为空，立即完成');
      this.consuming = false;
      clearTimeout(this.timer);
      if (this.onComplete) {
        this.onComplete();
      }
    } else {
      console.log('[Typewriter] 队列不为空或未在消费，等待队列消费完毕');
    }
  }

  // 停止打印
  stop() {
    this.consuming = false;
    this.queue = [];
    this.lastContent = '';
    this.displayedContent = '';
    this.isFinished = false;
  }
}
