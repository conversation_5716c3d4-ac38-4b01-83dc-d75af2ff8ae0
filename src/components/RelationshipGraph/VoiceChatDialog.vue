<template>
  <div class="dialog-overlay">
    <div class="dialog-container voice-chat-dialog">
      <div class="dialog-header">
        <div class="dialog-title">对话修改</div>
        <div class="dialog-close" @click="handleClose">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </div>
      </div>

      <div class="dialog-content voice-chat-content">
        <!-- 上部信息展示区域 -->
        <div class="info-display-section">
          <div class="section-header">
            <span class="section-icon">{{ sectionInfo.icon }}</span>
            <span class="section-title">{{ sectionInfo.title }}</span>
          </div>
          <div class="section-content">
            <div class="content-text">{{ sectionInfo.content }}</div>
          </div>
        </div>

        <!-- 下部语音对话区域 -->
        <div class="voice-chat-section">
          <!-- 聊天消息列表 -->
          <div ref="chatMessagesRef" class="chat-messages">
            <div v-for="message in chatMessages" :key="message.key" class="chat-message" :class="message.role">
              <div
                class="message-content"
                :class="{ 'loading-content': !message.isFinish && message.role === 'assistant' }"
              >
                <!-- 显示消息内容 -->
                <div v-if="message.content || message.isFinish">{{ message.content }}</div>
                <!-- 显示loading动画 -->
                <div v-else-if="!message.isFinish && message.role === 'assistant'" class="loading">
                  <div class="dot"></div>
                  <div class="dot"></div>
                  <div class="dot"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 语音输入区域 -->
          <div class="voice-input-area">
            <!-- 识别文字显示区域 -->
            <div v-if="recognizedText" class="recognized-text">
              {{ recognizedText }}
            </div>

            <!-- 默认状态：左侧语音按钮 + 中间输入框 + 右侧发送按钮 -->
            <div v-if="!isRecording" class="voice-input-default">
              <button class="voice-mic-btn" @click="startVoiceRecording">
                <div class="voice-button-bg"></div>
                <img src="@/assets/icon/mic.png" alt="语音" class="voice-mic-icon" />
              </button>

              <div class="text-input-container">
                <input
                  v-model="textInput"
                  type="text"
                  placeholder="输入消息..."
                  class="text-input"
                  @keydown.enter="handleTextSend"
                />
              </div>

              <button
                class="send-btn"
                :class="{
                  'not-input': !textInput.trim() || chatStore.answerStatus === AnswerStatusEnum.LOADING,
                }"
                @click="handleTextSend"
              >
                <i class="iconfont icon-mobile-send" class-prefix="icon"></i>
              </button>
            </div>

            <!-- 录音状态：左侧麦克风 + 语音条 -->
            <div v-else class="voice-input-recording">
              <div class="recording-mic-container">
                <button class="recording-mic-btn" @click="startVoiceRecording">
                  <div class="voice-button-bg recording"></div>
                  <img src="@/assets/icon/mic.png" alt="录音中" class="recording-mic-icon" />
                </button>
              </div>
              <div class="voice-wave">
                <div v-for="index in 50" :key="index" class="wave-line"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { streamChat, createConversation, type IToolCall } from '@/apis/chat';
import { Typewriter } from '@/utils/typeWriter';
import { useChatStore } from '@/stores/chat';
import { AnswerStatusEnum } from '@/constants/chat';
import { getStreamAsr } from '@/apis/chat';
import { generateRandomString } from '@/utils';
import { showToast } from 'vant';
import { debounce } from 'lodash-es';
import Recorder from 'recorder-realtime';

// 聊天消息类型定义
interface IChatStreamContent {
  role: 'user' | 'assistant';
  content: string;
  key: number | string;
  isFinish: boolean;
}

// Props定义
interface IProps {
  sectionInfo: {
    title: string;
    icon: string;
    content: string;
  };
  personName: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
}>();

// 聊天相关状态
const chatStore = useChatStore();
const chatMessages = ref<IChatStreamContent[]>([]);
const chatMessagesRef = ref<HTMLElement>();
const conversationId = ref('');
const streamController = ref<AbortController | null>(null);

// 打字机相关状态
const typewriter = new Typewriter(async (str: string) => {
  if (str && chatMessages.value.length > 0) {
    const lastMessage = chatMessages.value[chatMessages.value.length - 1];
    if (lastMessage.role === 'assistant') {
      lastMessage.content = str;
      await nextTick(() => {
        scrollChatToBottom();
      });
    }
  }
});
const isTypewriterStarted = ref(false);

// mention选择器相关
const mentionSelector = ref<HTMLElement>();
const mentionWidth = ref(120);

// 语音录音相关状态
const isRecording = ref(false);
const recognizedText = ref('');
const micPermission = ref(false);
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref<ArrayBuffer | null>(null);

// 文字输入相关状态
const textInput = ref('');
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 处理关闭
const handleClose = () => {
  // 清理正在进行的请求
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 重置聊天状态
  resetChatState();

  emit('close');
};

// 重置聊天状态
const resetChatState = () => {
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  isTypewriterStarted.value = false;
  typewriter.done();
};

// 初始化聊天会话
const initChatConversation = async () => {
  // 确保聊天状态是干净的
  resetChatState();

  if (!conversationId.value) {
    try {
      const response = await createConversation({
        user_id: props.userId,
      });
      if (response && response.conversation_id) {
        conversationId.value = response.conversation_id;
        console.log('✅ [VoiceChatDialog] 聊天会话初始化成功:', conversationId.value);
      }
    } catch (error) {
      console.error('❌ [VoiceChatDialog] 初始化聊天会话失败:', error);
    }
  }
};

// 滚动聊天到底部
const scrollChatToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight;
  }
};

// 更新mention选择器宽度
const updateMentionWidth = async () => {
  await nextTick();
  if (mentionSelector.value) {
    const rect = mentionSelector.value.getBoundingClientRect();
    mentionWidth.value = rect.width + 16; // 添加一些padding
  }
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 设置麦克风权限
const setMicPermission = async () => {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
};

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      if (streamData.data.text) {
        recognizedText.value = streamData.data.full_text;
        await autoSendTimeout();
      }
    }
  };
};

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopVoiceRecording();
}, 2000);

// 取消录音
const cancelRecording = () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }
  releaseMicrophoneResources();
  recognizedText.value = '';
};

// 开始语音录音
const startVoiceRecording = async () => {
  if (isRecording.value) {
    // 如果正在录音，取消录音
    cancelRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    recognizedText.value = '';
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopVoiceRecording();
    }, 1000 * 60);
  }
};

// 停止语音录音
const stopVoiceRecording = async () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (recognizedText.value) {
    // 直接发送语音识别的文字
    await handleVoiceSend(recognizedText.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
};

// 处理文字发送
const handleTextSend = async () => {
  // 如果输入框为空或正在加载中，直接返回
  if (!textInput.value.trim() || chatStore.answerStatus === AnswerStatusEnum.LOADING) return;

  const content = textInput.value.trim();
  textInput.value = ''; // 清空输入框

  await handleSendMessage(content);
};

// 处理语音发送
const handleVoiceSend = async (content: string) => {
  await handleSendMessage(content);
};

// 通用发送消息方法
const handleSendMessage = async (content: string) => {
  if (!content.trim() || chatStore.answerStatus === AnswerStatusEnum.LOADING) return;

  console.log('🚀 [VoiceChatDialog] 开始发送语音消息:', content);

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    console.log('🔄 [VoiceChatDialog] 取消正在进行的请求');
    streamController.value.abort();
    streamController.value = null;
  }

  // 清空识别文字
  recognizedText.value = '';

  // 重置状态
  isTypewriterStarted.value = false;
  typewriter.done(); // 确保打字机完全停止

  // 添加用户消息
  chatMessages.value.push({
    role: 'user',
    content,
    key: Date.now(),
    isFinish: true,
  });

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 1,
    isFinish: false,
  };
  chatMessages.value.push(assistantMessage);

  // 滚动到底部
  await nextTick(() => {
    scrollChatToBottom();
  });

  // 设置加载状态
  chatStore.setAnswerStatus(AnswerStatusEnum.LOADING);

  try {
    // 创建新的AbortController
    streamController.value = new AbortController();

    // 构建请求数据 - 包含section信息和用户问题
    const sectionContext = `当前正在修改${props.personName}的${props.sectionInfo.title}信息。当前内容：${props.sectionInfo.content}`;
    const finalContent = `${sectionContext}\n\n用户问题：${content}`;

    const requestData = {
      content: finalContent,
      conversation_id: conversationId.value,
      user_id: props.userId,
    };

    console.log('📤 [VoiceChatDialog] 发送聊天请求:', requestData);

    // 开始流式聊天
    await streamChat(
      requestData,
      {
        onMessage: (messageContent: string) => {
          // 添加数据片段到打字机队列
          typewriter.add(messageContent);

          // 只在第一个数据包到达时启动打字机
          if (!isTypewriterStarted.value) {
            isTypewriterStarted.value = true;
            typewriter.start();
          }
        },
        onPreResponse: (PreResponseContent: string, stage: string) => {
          console.log('🔍 [VoiceChatDialog] 收到预响应内容:', PreResponseContent, stage);
        },
        onToolCall: (toolCall: IToolCall) => {
          console.log('🔧 [VoiceChatDialog] 工具调用:', toolCall);
        },
        onRecommendations: (recommendations: string[]) => {
          console.log('💡 [VoiceChatDialog] 收到推荐问题:', recommendations);

          // 收到推荐问题时，标记消息完成
          assistantMessage.isFinish = true;

          // 结束打字机，立即显示剩余内容
          typewriter.done();

          // 重置状态
          chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);

          // 收到推荐问题表示整个对话真正结束，现在可以安全清空controller
          streamController.value = null;
          console.log('🏁 [VoiceChatDialog] 对话完全结束，清空streamController');
        },
        onEnd: () => {
          console.log('✅ [VoiceChatDialog] 收到end信号');

          // 注意：这里的end信号可能是工具调用结束，不一定是整个回答结束
          // 不应该设置 isFinish = true，因为回答可能还在继续
          // 只有收到 type: "recommendations" 时才是真正的回答结束

          // ⚠️ 不要在这里清空streamController，因为SSE连接可能还在活跃
          // streamController应该在onRecommendations或onError中清空
          console.log('🔄 [VoiceChatDialog] 保持SSE连接活跃，等待后续消息');
        },
        onError: (error: Error) => {
          console.error('❌ [VoiceChatDialog] 流式聊天错误:', error);
          assistantMessage.content = '抱歉，发生了错误，请稍后重试。';
          assistantMessage.isFinish = true;
          chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [VoiceChatDialog] 发送消息失败:', error);
    assistantMessage.content = '抱歉，发生了错误，请稍后重试。';
    assistantMessage.isFinish = true;
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  }

  // 滚动到底部
  await nextTick(() => {
    scrollChatToBottom();
  });
};

// 组件挂载时初始化
onMounted(async () => {
  await initChatConversation();
  await updateMentionWidth();
});

// 组件卸载时清理
onBeforeUnmount(() => {
  // 清理聊天状态
  resetChatState();

  // 清理语音录音资源
  if (isRecording.value) {
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  releaseMicrophoneResources();
});
</script>

<style scoped lang="scss">
// 弹窗遮罩层
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

// 弹窗容器
.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 600px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;

  &.voice-chat-dialog {
    max-width: 700px;
    height: 800px;
    overflow: hidden;
  }
}

// 弹窗头部
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .dialog-title {
    font-size: 24px; // 增加4px
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
  }

  .dialog-close {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.5);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
    }

    .close-icon {
      width: 20px;
      height: 20px;
      filter: brightness(0) invert(1);
    }
  }
}

// 弹窗内容
.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow: hidden;

  &.voice-chat-content {
    height: 100%;
  }
}

// 信息展示区域
.info-display-section {
  border: none;
  border-radius: 16px;
  padding: 20px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);

  .section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;

    .section-icon {
      font-size: 24px; // 增加4px
    }

    .section-title {
      font-size: 22px; // 增加4px
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  .section-content {
    .content-text {
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
      font-size: 18px; // 增加4px
      white-space: pre-wrap;
    }
  }
}

// 语音对话区域
.voice-chat-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: none;
  border-radius: 16px;
  padding: 20px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  overflow: hidden;
}

// 聊天消息列表
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

// 聊天消息
.chat-message {
  display: flex;
  margin-bottom: 16px;

  &.user {
    justify-content: flex-end;

    .message-content {
      background: rgba(0, 188, 212, 0.2);
      border: 1px solid rgba(0, 188, 212, 0.3);
      border-radius: 18px 18px 4px 18px;
      padding: 12px 16px;
      max-width: 70%;
      color: rgba(255, 255, 255, 0.9);
      font-size: 22px; // 增加4px
      line-height: 1.5;
      word-wrap: break-word;
    }
  }

  &.assistant {
    justify-content: flex-start;

    .message-content {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 18px 18px 18px 4px;
      padding: 12px 16px;
      max-width: 70%;
      color: rgba(255, 255, 255, 0.8);
      font-size: 22px; // 增加4px
      line-height: 1.5;
      word-wrap: break-word;

      &.loading-content {
        min-height: 40px;
        display: flex;
        align-items: center;
      }
    }
  }
}

// Loading动画
.loading {
  display: flex;
  gap: 4px;
  align-items: center;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    animation: loadingDot 1.4s infinite ease-in-out both;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }

    &:nth-child(3) {
      animation-delay: 0s;
    }
  }
}

@keyframes loadingDot {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 语音输入区域
.voice-input-area {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

// 识别文字显示区域 - 放在上半部分
.recognized-text {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  text-align: center;
  max-width: 80%;
  word-wrap: break-word;
  margin-bottom: 8px;
}

// 默认状态：左侧语音按钮 + 中间输入框 + 右侧发送按钮
.voice-input-default {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 16px;

  .voice-mic-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: 2px solid #00bcd4;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    flex-shrink: 0;

    &:hover {
      background: rgba(0, 188, 212, 0.1);
      border-color: #00bcd4;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }

    .voice-button-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      transition: all 0.3s ease;
    }

    .voice-mic-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%)
        contrast(96%);
      position: relative;
      z-index: 2;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .text-input-container {
    flex: 1;
    display: flex;
    align-items: center;

    .text-input {
      width: 100%;
      height: 56px;
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: 28px;
      padding: 0 20px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 20px;
      max-height: 165px;
      font-weight: 600;
      outline: none;
      transition: all 0.3s ease;
      backdrop-filter: blur(20px);

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        border-color: #00bcd4;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 4px rgba(0, 188, 212, 0.1);
      }
    }
  }

  .send-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: 2px solid #00bcd4;
    background: rgba(0, 188, 212, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    flex-shrink: 0;

    &:hover:not(.not-input) {
      background: rgba(0, 188, 212, 0.2);
      border-color: #00bcd4;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }

    &.not-input {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .iconfont {
      font-size: 40px;
      color: #00bcd4;
    }

    &:active:not(.not-input) {
      transform: scale(0.95);
    }
  }
}

// 录音状态：左侧麦克风 + 语音条
.voice-input-recording {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 20px;

  .recording-mic-container {
    display: flex;
    align-items: center;
    justify-content: center;

    .recording-mic-btn {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      border: 2px solid #00bcd4;
      background: rgba(255, 255, 255, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      backdrop-filter: blur(20px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

      &:hover {
        background: rgba(0, 188, 212, 0.1);
        border-color: #00bcd4;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
      }

      .voice-button-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        transition: all 0.3s ease;

        &.recording {
          background: rgba(0, 188, 212, 0.2);
          border: 2px solid #00bcd4;
          animation: voiceRecording 2s ease-in-out infinite;
          box-shadow: 0 0 0 0 rgba(0, 188, 212, 0.3);
        }
      }

      .recording-mic-icon {
        width: 24px;
        height: 24px;
        filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%)
          contrast(96%);
        position: relative;
        z-index: 2;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .voice-wave {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    height: 60px;

    .wave-line {
      width: 3px;
      background: linear-gradient(to top, #00bcd4, #00ffff);
      border-radius: 2px;
      animation: waveAnimation 1.5s ease-in-out infinite;

      &:nth-child(odd) {
        animation-delay: 0.1s;
      }

      &:nth-child(even) {
        animation-delay: 0.3s;
      }
    }
  }
}

// 动画定义
@keyframes voiceRecording {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 188, 212, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(0, 188, 212, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 20px rgba(0, 188, 212, 0);
  }
}

@keyframes waveAnimation {
  0%,
  100% {
    height: 10px;
  }
  50% {
    height: 40px;
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
