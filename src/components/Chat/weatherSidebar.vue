<template>
  <div class="weather-sidebar-container">
    <!-- 遮罩层 -->
    <div v-if="isOpen" class="sidebar-overlay" @click="handleClose"></div>
    <!-- 侧边栏 -->
    <div class="weather-sidebar" :class="{ 'sidebar-open': isOpen }">
      <div class="sidebar-header">
        <div class="title">天气助手</div>
        <div class="close-btn" @click="handleClose">
          <img src="@/assets/img/close.png" alt="关闭" />
        </div>
      </div>
      <div class="sidebar-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-text">加载中...</div>
        </div>
        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-text">{{ error }}</div>
          <div class="retry-btn" @click="fetchWeatherData">重试</div>
        </div>
        <!-- 天气内容 -->
        <div v-else class="weather-list">
          <!-- AI提醒信息区域 -->
          <div class="ai-reminder-section">
            <div class="summary-title">AI天气提醒</div>
            <div class="ai-reminder-content">
              <!-- 加载状态 -->
              <div v-if="loadingComprehensive" class="loading-state">
                <div class="loading-spinner"></div>
                <span class="loading-text">正在获取AI天气提醒...</span>
              </div>
              <!-- AI提醒内容 -->
              <div
                v-else-if="comprehensiveWeatherData && comprehensiveWeatherData.result === 'success'"
                class="ai-reminder-text"
              >
                {{ comprehensiveWeatherData.ai_reminder }}
              </div>
              <!-- 错误状态 -->
              <div v-else-if="comprehensiveError" class="error-state">
                <div class="error-icon">⚠️</div>
                <div class="error-text">{{ comprehensiveError }}</div>
              </div>
              <!-- 默认状态 -->
              <div v-else class="default-state">
                <div class="default-text">暂无AI天气提醒</div>
              </div>
            </div>
          </div>

          <!-- 天气相关信息区域 -->
          <div class="weather-related-section">
            <!-- 加载状态 -->
            <div v-if="loadingWeatherRelated" class="loading-container">
              <div class="loading-text">正在获取天气相关信息...</div>
            </div>
            <!-- 动态渲染分类 -->
            <div v-else-if="weatherCategories.length > 0">
              <div v-for="category in weatherCategories" :key="category.title" class="weather-category">
                <div class="category-title">{{ category.title }}</div>
                <div class="weather-items">
                  <div v-for="item in category.items" :key="item.label" class="weather-item">
                    <div class="weather-label">{{ item.label }}</div>
                    <div class="weather-value">{{ item.value }}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 错误状态 -->
            <div v-else-if="weatherRelatedError" class="error-container">
              <div class="error-text">{{ weatherRelatedError }}</div>
              <div class="retry-btn" @click="fetchWeatherRelatedData">重试</div>
            </div>
            <!-- 空状态 -->
            <div v-else class="empty-state">
              <div class="empty-text">暂无天气相关信息</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, ref, onMounted } from 'vue';
import { getUserInfo } from '@/apis/common';
import {
  getWeatherRelatedInfo,
  getComprehensiveWeather,
  type IGetWeatherRelatedInfoResponse,
  type IComprehensiveWeatherResponse,
} from '@/apis/memory';

// 定义天气数据接口
interface IWeatherItem {
  label: string;
  value: string;
}

interface IWeatherCategory {
  title: string;
  items: IWeatherItem[];
}

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['close']);

// 响应式数据
const loading = ref(false);
const error = ref('');
const weatherCategories = ref<IWeatherCategory[]>([]);
const weatherRelatedData = ref<IGetWeatherRelatedInfoResponse | null>(null);
const comprehensiveWeatherData = ref<IComprehensiveWeatherResponse | null>(null);
const currentUserId = ref<string>('');

// 分别控制两个API的加载状态
const loadingComprehensive = ref(false);
const loadingWeatherRelated = ref(false);
const comprehensiveError = ref('');
const weatherRelatedError = ref('');

// 假数据
const mockWeatherData: IWeatherCategory[] = [
  {
    title: '昨日天气',
    items: [
      { label: '天气状况', value: '晴转多云' },
      { label: '温度范围', value: '18°C - 26°C' },
      { label: '湿度', value: '65%' },
      { label: '风力风向', value: '东南风 2-3级' },
      { label: '空气质量', value: '良好 (AQI: 68)' },
    ],
  },
  {
    title: '昨日出行建议',
    items: [
      { label: '穿衣建议', value: '适合穿薄外套或长袖衬衫' },
      { label: '运动建议', value: '适合户外运动，建议早晚时段' },
      { label: '防晒建议', value: '紫外线中等，建议使用SPF30+防晒霜' },
      { label: '出行建议', value: '天气条件良好，适合出行' },
    ],
  },
  {
    title: '今日天气',
    items: [
      { label: '天气状况', value: '多云转阴' },
      { label: '温度范围', value: '20°C - 28°C' },
      { label: '湿度', value: '72%' },
      { label: '风力风向', value: '南风 3-4级' },
      { label: '空气质量', value: '优秀 (AQI: 45)' },
      { label: '降雨概率', value: '30%' },
    ],
  },
  {
    title: '今日出行建议',
    items: [
      { label: '穿衣建议', value: '建议穿长袖衬衫，可携带薄外套' },
      { label: '运动建议', value: '适合室内运动，户外运动注意防雨' },
      { label: '防晒建议', value: '紫外线较弱，可适当防护' },
      { label: '出行建议', value: '建议携带雨具，注意天气变化' },
      { label: '洗车建议', value: '不建议洗车，可能有降雨' },
    ],
  },
];

// 获取用户信息
const loadUserInfo = async () => {
  try {
    const userInfo = await getUserInfo();
    if (userInfo && userInfo.login) {
      currentUserId.value = userInfo.login;
      console.log('✅ [weatherSidebar] 用户信息加载成功, userId:', currentUserId.value);
    } else {
      console.warn('⚠️ [weatherSidebar] 用户信息格式异常');
      currentUserId.value = 'unknown_user';
    }
  } catch (err) {
    console.error('❌ [weatherSidebar] 获取用户信息失败:', err);
    currentUserId.value = 'unknown_user';
  }
};

// 获取综合天气数据
const fetchComprehensiveWeather = async () => {
  loadingComprehensive.value = true;
  comprehensiveError.value = '';

  try {
    console.log('🔄 [weatherSidebar] 开始获取综合天气数据...');

    // 确保有用户ID
    if (!currentUserId.value || currentUserId.value === 'unknown_user') {
      await loadUserInfo();
    }

    if (!currentUserId.value || currentUserId.value === 'unknown_user') {
      comprehensiveError.value = '无法获取用户信息';
      return;
    }

    const response = await getComprehensiveWeather({
      user_id: currentUserId.value,
    });

    console.log('📡 [weatherSidebar] 综合天气数据响应:', response);

    if (response && response.result === 'success') {
      comprehensiveWeatherData.value = response;
      console.log('✅ [weatherSidebar] 综合天气数据获取成功');
    } else {
      comprehensiveError.value = '综合天气数据获取失败';
      console.warn('⚠️ [weatherSidebar] 综合天气数据格式异常:', response);
    }
  } catch (err) {
    console.error('❌ [weatherSidebar] 获取综合天气数据失败:', err);
    comprehensiveError.value = '网络错误，请重试';
  } finally {
    loadingComprehensive.value = false;
  }
};

// 获取天气相关信息数据
const fetchWeatherRelatedData = async () => {
  loadingWeatherRelated.value = true;
  weatherRelatedError.value = '';

  try {
    console.log('🔄 [weatherSidebar] 开始获取天气相关信息...');

    // 确保有用户ID
    if (!currentUserId.value || currentUserId.value === 'unknown_user') {
      await loadUserInfo();
    }

    if (!currentUserId.value || currentUserId.value === 'unknown_user') {
      weatherRelatedError.value = '无法获取用户信息';
      return;
    }

    const response = await getWeatherRelatedInfo({
      user_id: currentUserId.value,
    });

    if (response && response.result === 'success') {
      weatherRelatedData.value = response;
      console.log('✅ [weatherSidebar] 天气相关数据获取成功');

      // 构建显示数据
      const categories: IWeatherCategory[] = [];

      // 添加相关人物信息
      if (response.weather_related_persons && response.weather_related_persons.length > 0) {
        const personItems: IWeatherItem[] = response.weather_related_persons.map((person) => {
          // 格式化 location_info
          const locationInfo = person.location_info
            ? Object.entries(person.location_info)
                .map(([key, value]) => `${key}: ${value}`)
                .join(', ')
            : '暂无位置信息';

          return {
            label: `${person.canonical_name} (${person.relationship})`,
            value: locationInfo,
          };
        });

        categories.push({
          title: '相关人物档案',
          items: personItems,
        });
      }

      // 添加相关事件信息
      if (response.weather_related_events && response.weather_related_events.length > 0) {
        const eventItems: IWeatherItem[] = response.weather_related_events.map((event) => ({
          label: event.location || '未知地点',
          value: event.description_text,
        }));

        categories.push({
          title: '相关事件',
          items: eventItems,
        });
      }

      // 如果没有数据，使用假数据
      if (categories.length === 0) {
        weatherCategories.value = mockWeatherData;
      } else {
        weatherCategories.value = categories;
      }
    } else {
      console.warn('⚠️ [weatherSidebar] 天气相关数据获取失败');
      weatherRelatedError.value = '天气相关数据获取失败';
      weatherCategories.value = mockWeatherData;
    }
  } catch (err) {
    console.error('❌ [weatherSidebar] 获取天气相关数据失败:', err);
    weatherRelatedError.value = '网络错误，请重试';
    // 出错时使用假数据
    weatherCategories.value = mockWeatherData;
  } finally {
    loadingWeatherRelated.value = false;
  }
};

// 获取天气数据（并行调用两个API）
const fetchWeatherData = async () => {
  loading.value = true;
  error.value = '';

  try {
    console.log('🔄 [weatherSidebar] 开始获取天气数据...');

    // 确保有用户ID
    if (!currentUserId.value || currentUserId.value === 'unknown_user') {
      await loadUserInfo();
    }

    if (!currentUserId.value || currentUserId.value === 'unknown_user') {
      error.value = '无法获取用户信息';
      return;
    }

    // 并行调用两个API
    void fetchComprehensiveWeather();
    void fetchWeatherRelatedData();

    console.log('✅ [weatherSidebar] 天气数据获取已启动');
  } catch (err) {
    console.error('❌ [weatherSidebar] 获取天气数据失败:', err);
    error.value = '网络错误，请重试';
  } finally {
    loading.value = false;
  }
};

// 关闭侧边栏
const handleClose = () => {
  emit('close');
};

// 监听侧边栏打开状态
watch(
  () => props.isOpen,
  (newVal) => {
    if (newVal) {
      document.body.style.overflow = 'hidden';
      // 侧边栏打开时获取数据
      void fetchWeatherData();
    } else {
      document.body.style.overflow = '';
    }
  },
);

// 组件挂载时如果侧边栏已经打开，则获取数据
onMounted(() => {
  if (props.isOpen) {
    void fetchWeatherData();
  }
});
</script>

<style lang="scss" scoped>
@import '@/styles/util.scss';
@import '@/styles/variable.scss';

.weather-sidebar-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
}

.weather-sidebar {
  position: absolute;
  top: 0;
  right: 0;
  width: 55%;
  height: 100%;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  box-shadow:
    var(--shadow-strong),
    0 0 0 1px var(--border-light);
  z-index: 1001;
  transform: translateX(100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  pointer-events: auto;

  &.sidebar-open {
    transform: translateX(0);
  }

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg) var(--spacing-xl);
    background: transparent;
    border-bottom: 1px solid var(--border-light);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: var(--spacing-xl);
      right: var(--spacing-xl);
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
    }

    .title {
      font-size: var(--font-size-2xl);
      font-weight: 600;
      color: var(--text-primary);
      letter-spacing: -0.5px;
    }

    .close-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius-sm);
      background: transparent;
      border: 1px solid transparent;
      color: var(--text-tertiary);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        background: var(--bg-hover);
        border-color: var(--border-light);
        color: var(--text-primary);
        transform: scale(1.05);
        box-shadow: var(--shadow-medium);
      }

      &:active {
        transform: scale(0.95);
      }

      img {
        width: 20px;
        height: 20px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
      }

      &:hover img {
        opacity: 1;
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
    background-color: transparent;
    /* 隐藏滚动条但保持可滚动 */
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .loading-text {
        font-size: 24px;
        color: var(--text-secondary);
        animation: pulse 1.5s ease-in-out infinite;
      }
    }

    .error-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 200px;
      gap: var(--spacing-md);

      .error-text {
        font-size: 24px;
        color: var(--text-tertiary);
        text-align: center;
      }

      .retry-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--primary-color);
        color: white;
        border-radius: var(--border-radius-sm);
        cursor: pointer;
        font-size: 22px;
        transition: all 0.3s ease;

        &:hover {
          background: var(--primary-color-hover);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .empty-text {
        font-size: 24px;
        color: var(--text-tertiary);
        text-align: center;
      }
    }

    .weather-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);

      // AI提醒区域样式
      .ai-reminder-section {
        margin-bottom: var(--spacing-lg);

        .summary-title {
          font-size: 22px;
          font-weight: 600;
          color: var(--text-secondary);
          margin-bottom: var(--spacing-sm);
          text-align: center;
          transition: color 0.3s ease;
        }

        .ai-reminder-content {
          height: 300px;
          padding: 20px;
          background: var(--primary-color-light);
          border-radius: 12px;
          border: 1px solid var(--border-glass);
          border-left: 3px solid var(--accent-color);
          overflow-y: auto;
          backdrop-filter: blur(10px);

          .ai-reminder-text {
            font-size: calc(var(--font-size-lg) + 4px);
            line-height: 1.6;
            color: var(--text-primary);
            white-space: pre-wrap;
          }

          // 加载状态样式
          .loading-state {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            height: 100%;
            color: var(--text-secondary);
          }

          .loading-spinner {
            width: 28px;
            height: 28px;
            border: 2px solid var(--border-glass);
            border-top: 2px solid var(--accent-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }

          .loading-text {
            font-size: 22px;
          }

          // 错误状态样式
          .error-state {
            display: flex;
            align-items: center;
            gap: 12px;
            height: 100%;
            justify-content: center;
            background: rgba(255, 107, 107, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 107, 107, 0.3);
          }

          .error-icon {
            font-size: var(--font-size-xl);
            flex-shrink: 0;
          }

          .error-text {
            font-size: var(--font-size-base);
            color: var(--text-secondary);
          }

          // 默认状态样式
          .default-state {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            color: var(--text-secondary);
          }

          .default-text {
            font-size: var(--font-size-base);
          }
        }
      }

      // 天气相关信息区域样式
      .weather-related-section {
        .loading-container {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 200px;

          .loading-text {
            font-size: 24px;
            color: var(--text-secondary);
            animation: pulse 1.5s ease-in-out infinite;
          }
        }

        .error-container {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 200px;
          gap: var(--spacing-md);

          .error-text {
            font-size: 24px;
            color: var(--text-tertiary);
            text-align: center;
          }

          .retry-btn {
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--primary-color);
            color: white;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            font-size: 22px;
            transition: all 0.3s ease;

            &:hover {
              background: var(--primary-color-hover);
              transform: translateY(-1px);
            }

            &:active {
              transform: translateY(0);
            }
          }
        }

        .empty-state {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 200px;

          .empty-text {
            font-size: 24px;
            color: var(--text-tertiary);
            text-align: center;
          }
        }
      }
    }

    .weather-category {
      .category-title {
        font-size: 26px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-sm);
        border-bottom: 2px solid var(--primary-color);
        letter-spacing: -0.3px;
      }

      .weather-items {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);

        .weather-item {
          padding: var(--spacing-md);
          border-radius: var(--border-radius-md);
          background: var(--bg-glass);
          border: 1px solid var(--border-light);
          backdrop-filter: blur(10px);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:hover {
            background: linear-gradient(135deg, rgba(0, 188, 212, 0.15) 0%, rgba(0, 255, 255, 0.1) 100%);
            border-color: rgba(0, 255, 255, 0.4);
            transform: translateY(-2px);
            box-shadow:
              0 6px 20px rgba(0, 255, 255, 0.2),
              0 0 15px rgba(0, 255, 255, 0.3);

            .weather-label {
              color: rgba(255, 255, 255, 0.9);
            }

            .weather-value {
              color: #ffffff;
            }
          }

          .weather-label {
            font-size: 22px;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 4px;
            transition: color 0.3s ease;
          }

          .weather-value {
            font-size: 22px;
            font-weight: 550;
            color: var(--text-primary);
            line-height: 1.4;
            transition: color 0.3s ease;
          }
        }
      }
    }
  }
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  z-index: 1000;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  transition: all 0.3s ease;
}

/* 添加一些微动画效果 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.weather-sidebar.sidebar-open {
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-overlay {
  animation: fadeIn 0.3s ease;
}
</style>
